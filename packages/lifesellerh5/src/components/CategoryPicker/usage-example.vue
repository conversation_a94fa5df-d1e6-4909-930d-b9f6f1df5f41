<template>
  <div class="category-picker-example">
    <h3>CategoryPicker 使用示例</h3>
    
    <!-- CategoryPicker 组件 -->
    <CategoryPicker
      v-model="selectedCategory"
      placeholder="请选择主营品类"
      title="选择主营品类"
      :max-levels="3"
      :last-level-multiple="false"
      @change="handleChange"
      @confirm="handleConfirm"
    />
    
    <!-- 显示选中的值 -->
    <div class="result-display">
      <h4>选中的值：</h4>
      <pre>{{ JSON.stringify(selectedCategory, null, 2) }}</pre>
    </div>
    
    <!-- 事件日志 -->
    <div class="event-log">
      <h4>事件日志：</h4>
      <div class="log-list">
        <div
          v-for="(log, index) in eventLogs"
          :key="index"
          class="log-item"
          :class="log.type"
        >
          <span class="log-time">{{ log.time }}</span>
          <span class="log-event">{{ log.event }}</span>
          <span class="log-data">{{ JSON.stringify(log.data) }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import CategoryPicker from './index.vue'
import type { CategoryPickerValue } from './types'

// 选中的分类值
const selectedCategory = ref<CategoryPickerValue>([])

// 事件日志
interface EventLog {
  time: string
  event: string
  data: CategoryPickerValue
  type: 'change' | 'confirm'
}

const eventLogs = ref<EventLog[]>([])

// 添加日志的辅助函数
const addLog = (event: string, data: CategoryPickerValue, type: 'change' | 'confirm') => {
  const now = new Date()
  const time = `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}:${now.getSeconds().toString().padStart(2, '0')}`
  
  eventLogs.value.unshift({
    time,
    event,
    data: [...data], // 创建副本避免引用问题
    type
  })
  
  // 限制日志数量
  if (eventLogs.value.length > 10) {
    eventLogs.value = eventLogs.value.slice(0, 10)
  }
}

// 处理 change 事件（数据变化时触发）
const handleChange = (value: CategoryPickerValue) => {
  console.log('CategoryPicker change event:', value)
  addLog('change', value, 'change')
}

// 处理 confirm 事件（用户点击确认按钮时触发）
const handleConfirm = (value: CategoryPickerValue) => {
  console.log('CategoryPicker confirm event:', value)
  addLog('confirm', value, 'confirm')
  
  // 在这里可以执行确认后的业务逻辑
  // 例如：提交表单、保存数据、跳转页面等
}
</script>

<style scoped lang="stylus">
.category-picker-example
  padding 20px
  max-width 800px
  margin 0 auto

h3, h4
  color #333
  margin-bottom 16px

.result-display
  margin-top 20px
  padding 16px
  background #f5f5f5
  border-radius 8px
  
  pre
    background white
    padding 12px
    border-radius 4px
    border 1px solid #e0e0e0
    font-size 12px
    overflow-x auto

.event-log
  margin-top 20px
  
.log-list
  max-height 300px
  overflow-y auto
  border 1px solid #e0e0e0
  border-radius 8px
  background white

.log-item
  display flex
  align-items center
  padding 8px 12px
  border-bottom 1px solid #f0f0f0
  font-size 12px
  
  &:last-child
    border-bottom none
  
  &.change
    background #f0f9ff
    
  &.confirm
    background #f0fff4

.log-time
  color #666
  margin-right 12px
  min-width 60px

.log-event
  font-weight 500
  margin-right 12px
  min-width 60px
  
  .change &
    color #0066cc
    
  .confirm &
    color #00aa44

.log-data
  color #333
  font-family monospace
  flex 1
  overflow hidden
  text-overflow ellipsis
  white-space nowrap
</style>
