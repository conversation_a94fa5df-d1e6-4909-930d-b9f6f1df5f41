/**
 * CategoryPicker 组件类型定义
 */

// 分类数据接口 - 兼容实际API返回的数据格式
export interface ICategory {
  id?: string
  name?: string
  isLeaf?: boolean // 实际API使用的是 isLeaf 而不是 is_leaf
  level?: number
}

// Picker 选项接口
export interface PickerOption {
  label: string
  value: string
  children?: PickerOption[]
}

// 选中值类型 - 数组形式的级联选择值
export type CategoryPickerValue = string[]

// 选中值的详细信息（内部使用）
export interface CategoryPickerValueDetail {
  selectedIds: string[] // 选中的ID数组，按层级顺序 ['level1-id', 'level2-id', 'level3-id']
  selectedNames: string[] // 选中的名称数组，按层级顺序 ['level1-name', 'level2-name', 'level3-name']
  categoryPath?: string // 分类路径，如 "美食/川菜/火锅"
  categoryId?: string // 最终选中的分类ID（叶子节点）
  level1?: string // 一级分类ID（兼容）
  level2?: string // 二级分类ID（兼容）
  level3?: string // 三级分类ID（兼容）
  level1Name?: string // 一级分类名称（兼容）
  level2Name?: string // 二级分类名称（兼容）
  level3Name?: string // 三级分类名称（兼容）
}

// 级联数据项接口
export interface CascadeItem {
  id: string
  name: string
  level: number
  isLeaf: boolean
  children?: CascadeItem[]
}

// 每一层级的数据
export interface LevelData {
  level: number
  categories: ICategory[]
  selectedId?: string
  selectedIds?: string[] // 多选模式下的选中项
}

// 组件 Props
export interface CategoryPickerProps {
  modelValue?: CategoryPickerValue | null
  placeholder?: string
  disabled?: boolean
  readonly?: boolean
  title?: string
  maxLevels?: number // 最大层级数，默认3，0表示不限制
  lastLevelMultiple?: boolean // 最后一层是否支持多选，默认false
  gridColumns?: number // 每行显示的列数，默认3
}

// 组件 Emits
export interface CategoryPickerEmits {
  (e: 'update:modelValue', value: CategoryPickerValue): void
  (e: 'change', value: CategoryPickerValue): void
  (e: 'confirm', value: CategoryPickerValue): void
}

// API 响应类型
export interface GetMarketableCategoriesResponse {
  itemInfos: ICategory[]
}

export interface GetChildrenCategoriesResponse {
  childrenInfos: ICategory[]
  paths: ICategory[]
}
