<template>
  <div class="claim-store-div">
    <header>
      <Header :title="'补充行业资质'"></Header>
    </header>
    <main>
      <ConfigProvider :color-mode="colorMode">
        <div class="form-item-layout">
          <!-- 主营品类选择 -->
          <article class="form-item form-item-layout">
            <p class="title">主营品类</p>
            <Form :data="formData" :rules="formRules" :show-submit="false" :align="FormType.ALIGN.RIGHT">
              <FormItem
                label="主营品类"
                name="category"
                :value="formData.category"
              >
                <CategoryPicker
                  v-model="formData.category"
                  title="选择主营品类"
                  @close="closeCategoryPicker"
                  @confirm="handleCategoryConfirm"
                />
              </FormItem>
              <FormItem
                v-if="qualificationList.length > 0"
                label="资质类型"
                name="qualificationList"
                :value="formData.qualificationList"
                required
              >
                <div v-for="(qualification, index) in qualificationList" :key="index" class="qualification-wrapper">
                  <QualificationCard
                    v-model="formData.qualificationList[qualification.qualificationCode]"
                    :component-props="{
                      categoryId: selectedCategoryId,
                      readonly: false,
                      disabled: false,
                      onLoad: handleQualificationLoad
                    }"
                  />
                </div>
              </FormItem>
            </Form>
          </article>
        </div>
      </ConfigProvider>
    </main>

    <!-- 底部按钮 -->
    <footer>
      <ConfigProvider :color-mode="colorMode">
        <div class="footer-buttons">
          <Button
            type="secondary"
            variant="outline"
            block
            @click="handlePrevious"
          >
            上一步
          </Button>
          <Button
            type="primary"
            :variant="canSubmit ? 'fill' : 'disabled'"
            :disabled="!canSubmit"
            block
            @click="handleSubmit"
          >
            提交审核
          </Button>
        </div>
      </ConfigProvider>
    </footer>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useRouter } from 'vue-router'
import { useStore } from 'vuex'
import {
  Button,
  ConfigProvider,
  showToast,
  Form,
  FormItem,
  FormType,
} from '@xhs/reds-h5-next'

// 接口
import { getQueryQualificationConfigget } from '~/services/edith_get_query_qualification_configget'

// 组件
import Header from '~/components/header/index.vue'
import CategoryPicker from '~/components/CategoryPicker/index.vue'
import QualificationCard from '~/components/QualificationCard/components/H5/QualificationCard.vue'

// 类型
import type { CategoryPickerValue } from '~/components/CategoryPicker/types'
import type { QualificationItem } from '~/components/QualificationCard/core/type'

// 静态资源
import '~/assets/svg/arrowRightRightM.svg'

// 初始化
const router = useRouter()
const store = useStore()
const { colorMode } = store.state.claimStore

// 表单数据
const formData = ref({
  category: null,
  qualificationList: []
})

const formRules = ref({
  category: [{ required: true, message: '请选择主营品类' }]
})

// 响应式数据
const categoryPickerVisible = ref(false)
const selectedCategory = ref<CategoryPickerValue | null>(null)
const qualificationList = ref<any[]>([])
const qualificationData = ref<Record<string, QualificationItem>>({})

// 计算属性
const selectedCategoryId = computed(() => {
  if (!selectedCategory.value || selectedCategory.value.length === 0) return ''
  return selectedCategory.value[selectedCategory.value.length - 1]
})

const canSubmit = computed(() => {
  // 检查是否选择了主营品类
  if (!selectedCategory.value || selectedCategory.value.length === 0) {
    return false
  }

  // 检查资质是否填写完整
  if (qualificationList.value.length === 0) {
    return false
  }

  // 检查每个资质项是否都有必要的数据
  return qualificationList.value.every(qual => {
    const data = qualificationData.value[qual.qualificationCode]
    return data && data.mediaInfoList && data.mediaInfoList.length > 0
  })
})

const closeCategoryPicker = () => {
  categoryPickerVisible.value = false
}

const handleCategoryConfirm = async (value: CategoryPickerValue | null) => {
  loadQualificationTypes()
}s

const loadQualificationTypes = async () => {
  if (!selectedCategoryId.value) return

  try {
    const result = await getQueryQualificationConfigget({
      categoryId: formData.value.category[formData.value.category.length - 1]
    })

    if (result?.qualificationGroupList) {
      const types: any[] = []
      result.qualificationGroupList.forEach(group => {
        if (group.qualificationElements) {
          group.qualificationElements.forEach(element => {
            if (element.qualificationConfig) {
              types.push(element.qualificationConfig)
            }
          })
        }
      })
      qualificationList.value = types
    }
  } catch (error: any) {
    showToast('获取资质配置失败')
  }
}

const handleQualificationLoad = (loaded: boolean) => {
  console.log('资质配置加载:', loaded)
}

const handlePrevious = () => {
  router.back()
}

const handleSubmit = () => {
  if (!canSubmit.value) {
    showToast('请完善资质信息')
    return
  }

  showToast('提交成功')

  router.back()
}
</script>

<style lang="stylus" scoped>
.claim-store-div {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100vh;
  background: #F5F5F5;
  padding-top: 24px;

  @supports (padding-top: constant(safe-area-inset-top)) {
    padding-top: constant(safe-area-inset-top);
  }
  @supports (padding-top: env(safe-area-inset-top)) {
    padding-top: env(safe-area-inset-top);
  }

  header {
    flex: 0 0 auto;
  }

  main {
    flex: 1 1 0;
    min-height: 0;
    padding: 0 16px;
    margin-top: 16px;
    overflow: auto;
    -webkit-overflow-scrolling: touch;

    .form-item {
      width: 100%;

      .title {
        margin: 0 0 12px 8px;
        color: rgba(0, 0, 0, 0.62);
        font-family: "PingFang SC";
        font-size: 12px;
        font-style: normal;
        font-weight: 400;
        line-height: 18px;
      }

      .form-item-box {
        width: 100%;
        height: 100%;
        padding-left: 16px;
        background: #FFF;

        &.border-radius-top {
          border-radius: 8px 8px 0 0;
        }
      }

      .item {
        padding: 16px 16px 16px 0;
        border-bottom: 0.5px solid rgba(0, 0, 0, 0.08);

        &.border-top {
          border-top: none;
        }

        &.border-radius-top {
          border-radius: 8px 8px 0 0;
        }

        .sheets {
          display: flex;
          align-items: center;
          justify-content: space-between;
          cursor: pointer;

          .sheets-empty {
            color: rgba(0, 0, 0, 0.45);
          }

          .onix-icon-16 {
            width: 16px;
            height: 16px;
            color: rgba(0, 0, 0, 0.45);
          }
        }
      }
    }

    .form-item-layout {
      margin-bottom: 16px;
    }

    .qualification-wrapper {
      background: #FFF;
      border-radius: 8px;
      margin-bottom: 16px;
      padding: 16px;
    }
  }

  footer {
    background: #fff;
    flex: 0 0 auto;
    padding: 16px;

    @supports (padding-bottom: env(safe-area-inset-bottom, 16px)) {
      padding-bottom: env(safe-area-inset-bottom, 16px);
    }
    @supports (padding-bottom: constant(safe-area-inset-bottom, 16px)) {
      padding-bottom: constant(safe-area-inset-bottom, 16px);
    }

    .footer-buttons {
      display: flex;
      gap: 12px;
    }
  }
}
</style>
